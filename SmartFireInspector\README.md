# SmartFire Inspector

<PERSON><PERSON><PERSON><PERSON><PERSON> systém pre správu požiarnych inš<PERSON>k<PERSON><PERSON> s mobilnou aplikáciou, webovým administračným panelom a REST API.

## 🏗️ Architektúra projektu

```
SmartFireInspector/
├── mobile/                    # Flutter mobilná aplikácia
├── web-admin/                # React webová aplikácia (admin panel)
├── api/                      # .NET Core Web API
├── database/                 # Databázové skripty a migrácie
└── docs/                     # Dokumentácia
```

## 🚀 Technológie

### Backend API
- **.NET Core 8** - Web API
- **Dapper** - Micro-ORM pre databázové operácie
- **JWT Bearer Authentication** - Autentifikácia
- **Npgsql** - PostgreSQL connector

### Frontend (Web Admin)
- **React 19** - UI framework
- **TypeScript** - Typová bezpečnosť
- **Vite** - Build tool
- **Tailwind CSS** - Št<PERSON>lovanie
- **Axios** - HTTP klient
- **React Router** - Routing

### Mobilná aplikácia
- **Flutter** - Cross-platform framework
- **Dart** - Programovací jazyk

### Databáza a úložisko
- **Supabase** - PostgreSQL databáza + Storage + Auth
- **Row Level Security** - Bezpečnosť na úrovni riadkov
- **Storage buckets** - Úložisko obrázkov

## 🔧 Spustenie projektu

### 1. API Server (.NET Core)

```bash
cd api/SmartFireInspector.Api
dotnet restore
dotnet run
```

API bude dostupné na: `https://localhost:7000` (alebo iný port zobrazený v konzole)

### 2. Web Admin Panel (React)

```bash
cd web-admin
npm install
npm run dev
```

Web aplikácia bude dostupná na: `http://localhost:5173`

### 3. Mobilná aplikácia (Flutter)

```bash
cd mobile
flutter pub get
flutter run
```

## 🔐 Testovacie kontá

API obsahuje tri natvrdo vytvorené testovacie kontá:

| Email | Heslo | Rola | Meno |
|-------|-------|------|------|
| `<EMAIL>` | `admin123` | Admin | Admin Používateľ |
| `<EMAIL>` | `inspector123` | Inspector | Ján Inšpektor |
| `<EMAIL>` | `manager123` | Manager | Mária Manažérka |

## 📋 API Endpointy

### Autentifikácia
- `POST /api/auth/login` - Prihlásenie používateľa
- `GET /api/auth/test-accounts` - Zoznam testovacích kontov

### Príklad prihlásenia

```bash
curl -X POST https://localhost:7000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

## 🔧 Konfigurácia

### API konfigurácia (appsettings.json)

```json
{
  "Jwt": {
    "Key": "SuperSecretKeyForSmartFireInspector2024!",
    "Issuer": "SmartFireInspector",
    "Audience": "SmartFireInspector"
  }
}
```

### CORS nastavenia

API je nakonfigurované pre CORS s povolením pre:
- `http://localhost:3000` (React dev server)
- `http://localhost:5173` (Vite dev server)

## 🎯 Funkcionality

### ✅ Implementované
- [x] .NET Core Web API s JWT autentifikáciou
- [x] React webová aplikácia s prihlasovacou obrazovkou
- [x] Responzívny dizajn s Tailwind CSS
- [x] TypeScript typy a služby
- [x] Context API pre state management
- [x] Testovacie kontá
- [x] CORS konfigurácia
- [x] Dashboard s základnými štatistikami

### 🚧 Plánované
- [ ] Supabase integrácia
- [ ] Správa používateľov
- [ ] Správa inšpekcií
- [ ] Upload a správa obrázkov
- [ ] Flutter mobilná aplikácia
- [ ] Real-time notifikácie
- [ ] Reporting a exporty

## 🛠️ Vývoj

### Pridanie nových funkcionalít

1. **Backend**: Pridajte nové controllery do `api/SmartFireInspector.Api/Controllers/`
2. **Frontend**: Vytvorte nové komponenty v `web-admin/src/components/`
3. **Typy**: Definujte TypeScript typy v `web-admin/src/types/`
4. **Služby**: Pridajte API volania do `web-admin/src/services/`

### Štruktúra databázy

Databázová schéma bude vytvorená v Supabase s nasledujúcimi hlavnými tabuľkami:
- `users` - Používatelia systému
- `inspections` - Požiarne inšpekcie
- `inspection_items` - Položky inšpekcií
- `images` - Metadata obrázkov
- `reports` - Vygenerované reporty

## 📝 Poznámky

- Projekt používa hybridný prístup: Supabase pre storage a auth, .NET API pre business logiku
- Všetky hesla sú hashované pomocí SHA256 + salt
- JWT tokeny majú platnosť 8 hodín
- Aplikácia je pripravená na multi-tenant architektúru
