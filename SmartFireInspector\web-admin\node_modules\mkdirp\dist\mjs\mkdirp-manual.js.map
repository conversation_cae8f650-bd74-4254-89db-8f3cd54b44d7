{"version": 3, "file": "mkdirp-manual.js", "sourceRoot": "", "sources": ["../../src/mkdirp-manual.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,CAAA;AAC9B,OAAO,EAAiB,OAAO,EAAE,MAAM,eAAe,CAAA;AAEtD,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAC9B,IAAY,EACZ,OAAuB,EACvB,IAAgC,EACL,EAAE;IAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC5B,MAAM,IAAI,GAAG,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAA;IAEtD,IAAI,MAAM,KAAK,IAAI,EAAE;QACnB,IAAI;YACF,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;SAClC;QAAC,OAAO,EAAE,EAAE;YACX,yDAAyD;YACzD,+BAA+B;YAC/B,MAAM,GAAG,GAAG,EAA2B,CAAA;YACvC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAChC,MAAM,EAAE,CAAA;aACT;YACD,OAAM;SACP;KACF;IAED,IAAI;QACF,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC1B,OAAO,IAAI,IAAI,IAAI,CAAA;KACpB;IAAC,OAAO,EAAE,EAAE;QACX,MAAM,GAAG,GAAG,EAA2B,CAAA;QACvC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;YAChC,OAAO,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;SAC1E;QACD,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO,EAAE;YAC/D,MAAM,EAAE,CAAA;SACT;QACD,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;gBAAE,MAAM,EAAE,CAAA;SACjD;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,EAAE,CAAA;SACT;KACF;AACH,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CACvC,KAAK,EACH,IAAY,EACZ,OAAuB,EACvB,IAAgC,EACI,EAAE;IACtC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;IAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;IACtB,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC5B,IAAI,MAAM,KAAK,IAAI,EAAE;QACnB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;YAC5C,yDAAyD;YACzD,+BAA+B;YAC/B,MAAM,GAAG,GAAG,EAA2B,CAAA;YACvC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAChC,MAAM,EAAE,CAAA;aACT;QACH,CAAC,CAAC,CAAA;KACH;IAED,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CACrC,GAAG,EAAE,CAAC,IAAI,IAAI,IAAI,EAClB,KAAK,EAAC,EAAE,EAAC,EAAE;QACT,MAAM,GAAG,GAAG,EAA2B,CAAA;QACvC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;YAChC,OAAO,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CACpC,CAAC,IAAgC,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CACrE,CAAA;SACF;QACD,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO,EAAE;YACxD,MAAM,EAAE,CAAA;SACT;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAC9B,EAAE,CAAC,EAAE;YACH,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;gBACpB,OAAO,IAAI,CAAA;aACZ;iBAAM;gBACL,MAAM,EAAE,CAAA;aACT;QACH,CAAC,EACD,GAAG,EAAE;YACH,MAAM,EAAE,CAAA;QACV,CAAC,CACF,CAAA;IACH,CAAC,CACF,CAAA;AACH,CAAC,EACD,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAC3B,CAAA", "sourcesContent": ["import { dirname } from 'path'\nimport { MkdirpOptions, optsArg } from './opts-arg.js'\n\nexport const mkdirpManualSync = (\n  path: string,\n  options?: MkdirpOptions,\n  made?: string | undefined | void\n): string | undefined | void => {\n  const parent = dirname(path)\n  const opts = { ...optsArg(options), recursive: false }\n\n  if (parent === path) {\n    try {\n      return opts.mkdirSync(path, opts)\n    } catch (er) {\n      // swallowed by recursive implementation on posix systems\n      // any other error is a failure\n      const fer = er as NodeJS.ErrnoException\n      if (fer && fer.code !== 'EISDIR') {\n        throw er\n      }\n      return\n    }\n  }\n\n  try {\n    opts.mkdirSync(path, opts)\n    return made || path\n  } catch (er) {\n    const fer = er as NodeJS.ErrnoException\n    if (fer && fer.code === 'ENOENT') {\n      return mkdirpManualSync(path, opts, mkdirpManualSync(parent, opts, made))\n    }\n    if (fer && fer.code !== 'EEXIST' && fer && fer.code !== 'EROFS') {\n      throw er\n    }\n    try {\n      if (!opts.statSync(path).isDirectory()) throw er\n    } catch (_) {\n      throw er\n    }\n  }\n}\n\nexport const mkdirpManual = Object.assign(\n  async (\n    path: string,\n    options?: MkdirpOptions,\n    made?: string | undefined | void\n  ): Promise<string | undefined | void> => {\n    const opts = optsArg(options)\n    opts.recursive = false\n    const parent = dirname(path)\n    if (parent === path) {\n      return opts.mkdirAsync(path, opts).catch(er => {\n        // swallowed by recursive implementation on posix systems\n        // any other error is a failure\n        const fer = er as NodeJS.ErrnoException\n        if (fer && fer.code !== 'EISDIR') {\n          throw er\n        }\n      })\n    }\n\n    return opts.mkdirAsync(path, opts).then(\n      () => made || path,\n      async er => {\n        const fer = er as NodeJS.ErrnoException\n        if (fer && fer.code === 'ENOENT') {\n          return mkdirpManual(parent, opts).then(\n            (made?: string | undefined | void) => mkdirpManual(path, opts, made)\n          )\n        }\n        if (fer && fer.code !== 'EEXIST' && fer.code !== 'EROFS') {\n          throw er\n        }\n        return opts.statAsync(path).then(\n          st => {\n            if (st.isDirectory()) {\n              return made\n            } else {\n              throw er\n            }\n          },\n          () => {\n            throw er\n          }\n        )\n      }\n    )\n  },\n  { sync: mkdirpManualSync }\n)\n"]}