// Get the canvas element and its context
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// Game settings
const gridSize = 20;
const tileCount = canvas.width / gridSize;
let speed = 7; // frames per second

// Snake initial position and size
let snake = [
    { x: 10, y: 10 }
];
let snakeLength = 1;

// Snake movement direction
let dx = 0;
let dy = 0;

// Food position
let foodX;
let foodY;

// Score
let score = 0;

// Game state
let gameOver = false;

// Initialize the game
function init() {
    snake = [{ x: 10, y: 10 }];
    snakeLength = 1;
    dx = 0;
    dy = 0;
    score = 0;
    gameOver = false;
    document.getElementById('score').textContent = score;
    generateFood();
    main();
}

// Generate random food position
function generateFood() {
    foodX = Math.floor(Math.random() * tileCount);
    foodY = Math.floor(Math.random() * tileCount);

    // Make sure food doesn't spawn on the snake
    for (let i = 0; i < snake.length; i++) {
        if (snake[i].x === foodX && snake[i].y === foodY) {
            generateFood();
            return;
        }
    }
}

// Draw the snake
function drawSnake() {
    ctx.fillStyle = 'blue';
    for (let i = 0; i < snake.length; i++) {
        ctx.fillRect(snake[i].x * gridSize, snake[i].y * gridSize, gridSize, gridSize);

        // Draw a border around each snake part
        ctx.strokeStyle = 'darkblue';
        ctx.strokeRect(snake[i].x * gridSize, snake[i].y * gridSize, gridSize, gridSize);
    }
}

// Draw the food
function drawFood() {
    ctx.fillStyle = 'red';
    ctx.fillRect(foodX * gridSize, foodY * gridSize, gridSize, gridSize);

    // Add a little shine to the food
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.beginPath();
    ctx.arc(foodX * gridSize + gridSize / 4, foodY * gridSize + gridSize / 4, gridSize / 8, 0, Math.PI * 2);
    ctx.fill();
}

// Move the snake
function moveSnake() {
    // Create a new head based on the direction
    const head = { x: snake[0].x + dx, y: snake[0].y + dy };

    // Add the new head to the beginning of the snake array
    snake.unshift(head);

    // Check if snake ate the food
    if (head.x === foodX && head.y === foodY) {
        // Increase score
        score++;
        document.getElementById('score').textContent = score;

        // Increase snake length
        snakeLength++;

        // Generate new food
        generateFood();

        // Increase speed slightly
        if (score % 5 === 0) {
            speed += 1;
        }
    } else {
        // Remove the tail if the snake didn't eat
        while (snake.length > snakeLength) {
            snake.pop();
        }
    }
}

// Check for collisions
function checkCollision() {
    const head = snake[0];

    // Check wall collision
    if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
        gameOver = true;
        return;
    }

    // Check self collision (start from 1 to avoid checking the head against itself)
    for (let i = 1; i < snake.length; i++) {
        if (head.x === snake[i].x && head.y === snake[i].y) {
            gameOver = true;
            return;
        }
    }
}

// Draw game over screen
function drawGameOver() {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.75)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.fillStyle = 'white';
    ctx.font = '30px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Game Over!', canvas.width / 2, canvas.height / 2 - 30);

    ctx.font = '20px Arial';
    ctx.fillText(`Score: ${score}`, canvas.width / 2, canvas.height / 2 + 10);

    ctx.fillText('Press Restart to play again', canvas.width / 2, canvas.height / 2 + 50);
}

// Main game loop
function main() {
    if (gameOver) {
        drawGameOver();
        return;
    }

    setTimeout(function() {
        // Clear the canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw grid lines
        drawGrid();

        // Draw food
        drawFood();

        // Move snake
        moveSnake();

        // Check for collisions
        checkCollision();

        // Draw snake
        drawSnake();

        // Call main again
        main();
    }, 1000 / speed);
}

// Draw grid lines
function drawGrid() {
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 0.5;

    for (let i = 0; i <= tileCount; i++) {
        // Vertical lines
        ctx.beginPath();
        ctx.moveTo(i * gridSize, 0);
        ctx.lineTo(i * gridSize, canvas.height);
        ctx.stroke();

        // Horizontal lines
        ctx.beginPath();
        ctx.moveTo(0, i * gridSize);
        ctx.lineTo(canvas.width, i * gridSize);
        ctx.stroke();
    }
}

// Handle keyboard input
document.addEventListener('keydown', function(event) {
    // Prevent the snake from reversing direction
    switch (event.key) {
        case 'ArrowUp':
            if (dy !== 1) { // Not moving down
                dx = 0;
                dy = -1;
            }
            break;
        case 'ArrowDown':
            if (dy !== -1) { // Not moving up
                dx = 0;
                dy = 1;
            }
            break;
        case 'ArrowLeft':
            if (dx !== 1) { // Not moving right
                dx = -1;
                dy = 0;
            }
            break;
        case 'ArrowRight':
            if (dx !== -1) { // Not moving left
                dx = 1;
                dy = 0;
            }
            break;
    }
});

// Restart button
document.getElementById('restartButton').addEventListener('click', init);

// Start the game
generateFood();
main();
